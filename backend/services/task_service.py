"""
任务管理服务
Task Management Service
"""

import uuid
from datetime import datetime
from typing import Any

from beanie import PydanticObjectId
from loguru import logger

from ..models.task import (
    Task,
    TaskCreate,
    TaskResponse,
    TaskStatus,
    TaskType,
)
from ..tasks import get_task_config


class TaskService:
    """任务管理服务"""

    def __init__(self):
        pass

    async def create_task(self, task_data: TaskCreate) -> TaskResponse:
        """创建新任务"""
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 获取任务配置
            config = get_task_config(task_data.task_type.value)

            # 转换conversation_id和message_id为ObjectId
            conversation_id = None
            if task_data.conversation_id:
                conversation_id = PydanticObjectId(task_data.conversation_id)

            message_id = None
            if task_data.message_id:
                message_id = PydanticObjectId(task_data.message_id)

            # 创建数据库记录
            task = Task(
                task_id=task_id,
                task_type=task_data.task_type,
                task_name=task_data.task_name,
                description=task_data.description,
                priority=task_data.priority,
                conversation_id=conversation_id,
                message_id=message_id,
                user_id=task_data.user_id,
                input_data=task_data.input_data,
                metadata=task_data.metadata,
                queue_name=task_data.queue_name or config.get("queue", "default"),
                max_retries=task_data.max_retries,
                estimated_duration=task_data.estimated_duration,
                status=TaskStatus.PENDING,
                created_at=datetime.utcnow(),
            )

            # 保存到MongoDB
            await task.insert()

            logger.info(f"Created task: {task_id} ({task_data.task_type.value})")

            # 转换为响应模型
            return TaskResponse(
                id=str(task.id),
                task_id=task.task_id,
                task_type=task.task_type,
                task_name=task.task_name,
                description=task.description,
                priority=task.priority,
                status=task.status,
                progress=task.progress,
                conversation_id=str(task.conversation_id) if task.conversation_id else None,
                message_id=str(task.message_id) if task.message_id else None,
                user_id=task.user_id,
                input_data=task.input_data,
                output_data=task.output_data,
                metadata=task.metadata,
                worker_id=task.worker_id,
                queue_name=task.queue_name,
                retry_count=task.retry_count,
                max_retries=task.max_retries,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                estimated_duration=task.estimated_duration,
                actual_duration=task.actual_duration,
                error_message=task.error_message,
                error_code=task.error_code,
                error_details=task.error_details,
                is_cancelled=task.is_cancelled,
                is_system_task=task.is_system_task,
            )

        except Exception as e:
            logger.error(f"Failed to create task: {e}")
            raise

    async def get_task(self, task_id: str) -> TaskResponse | None:
        """获取任务详情"""
        try:
            task = await Task.find_one(Task.task_id == task_id)

            if not task:
                return None

            return TaskResponse(
                id=str(task.id),
                task_id=task.task_id,
                task_type=task.task_type,
                task_name=task.task_name,
                description=task.description,
                priority=task.priority,
                status=task.status,
                progress=task.progress,
                conversation_id=str(task.conversation_id) if task.conversation_id else None,
                message_id=str(task.message_id) if task.message_id else None,
                user_id=task.user_id,
                input_data=task.input_data,
                output_data=task.output_data,
                metadata=task.metadata,
                worker_id=task.worker_id,
                queue_name=task.queue_name,
                retry_count=task.retry_count,
                max_retries=task.max_retries,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                estimated_duration=task.estimated_duration,
                actual_duration=task.actual_duration,
                error_message=task.error_message,
                error_code=task.error_code,
                error_details=task.error_details,
                is_cancelled=task.is_cancelled,
                is_system_task=task.is_system_task,
            )

        except Exception as e:
            logger.error(f"Failed to get task {task_id}: {e}")
            raise

    async def list_tasks_by_user(self, user_id: str = None, limit: int = 50) -> list[TaskResponse]:
        """获取用户的任务列表"""
        try:
            query = {}
            if user_id:
                query["user_id"] = user_id

            tasks = await Task.find(query).limit(limit).sort(-Task.created_at).to_list()

            task_responses = []
            for task in tasks:
                task_responses.append(TaskResponse(
                    id=str(task.id),
                    task_id=task.task_id,
                    task_type=task.task_type,
                    task_name=task.task_name,
                    description=task.description,
                    priority=task.priority,
                    status=task.status,
                    progress=task.progress,
                    conversation_id=str(task.conversation_id) if task.conversation_id else None,
                    message_id=str(task.message_id) if task.message_id else None,
                    user_id=task.user_id,
                    input_data=task.input_data,
                    output_data=task.output_data,
                    metadata=task.metadata,
                    worker_id=task.worker_id,
                    queue_name=task.queue_name,
                    retry_count=task.retry_count,
                    max_retries=task.max_retries,
                    created_at=task.created_at,
                    started_at=task.started_at,
                    completed_at=task.completed_at,
                    estimated_duration=task.estimated_duration,
                    actual_duration=task.actual_duration,
                    error_message=task.error_message,
                    error_code=task.error_code,
                    error_details=task.error_details,
                    is_cancelled=task.is_cancelled,
                    is_system_task=task.is_system_task,
                ))

            return task_responses

        except Exception as e:
            logger.error(f"Failed to list tasks: {e}")
            raise



    async def create_animation_task(
        self,
        text: str,
        character_id: str = "default",
        user_id: str = None,
        context: dict[str, Any] = None,
    ) -> TaskResponse:
        """创建动画生成任务记录"""
        try:
            # 创建任务记录
            task_data = TaskCreate(
                task_type=TaskType.ANIMATION_GENERATION,
                task_name=f"Generate animation: {text[:50]}...",
                description=f"Generate animation for: {text}",
                user_id=user_id,
                input_data={
                    "text": text,
                    "character_id": character_id,
                    "context": context or {},
                },
            )

            task_response = await self.create_task(task_data)
            logger.info(f"Created animation task: {task_response.task_id}")
            return task_response

        except Exception as e:
            logger.error(f"Failed to create animation task: {e}")
            raise

    async def update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        output_data: dict[str, Any] = None,
        error_message: str = None,
    ) -> TaskResponse | None:
        """更新任务状态"""
        try:
            task = await Task.find_one(Task.task_id == task_id)
            if not task:
                return None

            task.status = status
            if output_data:
                task.output_data.update(output_data)
            if error_message:
                task.error_message = error_message

            if status == TaskStatus.RUNNING and not task.started_at:
                task.started_at = datetime.utcnow()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                if not task.completed_at:
                    task.completed_at = datetime.utcnow()

                # 计算实际执行时间
                if task.started_at:
                    duration = (task.completed_at - task.started_at).total_seconds()
                    task.actual_duration = int(duration)

            await task.save()

            logger.info(f"Updated task {task_id} status to {status.value}")
            return await self.get_task(task_id)

        except Exception as e:
            logger.error(f"Failed to update task {task_id}: {e}")
            raise

