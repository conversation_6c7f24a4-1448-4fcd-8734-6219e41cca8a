#!/usr/bin/env python3
"""
Mock Backend Server for Motion Agent Frontend Testing
使用Python标准库创建的简单HTTP服务器，用于测试frontend功能
"""

import json
import time
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class MockMotionAgentHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 设置CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Content-Type', 'application/json')
        
        if path == '/health':
            self.handle_health()
        elif path == '/animation/health':
            self.handle_animation_health()
        elif path == '/animation/presets':
            self.handle_animation_presets()
        elif path == '/animation/examples':
            self.handle_animation_examples()
        elif path == '/conversations':
            self.handle_conversations_list()
        elif path == '/tasks':
            self.handle_tasks_list()
        elif path == '/tasks/stats/overview':
            self.handle_task_stats()
        elif path.startswith('/conversations/') and path.endswith('/history'):
            self.handle_conversation_history()
        elif path.startswith('/tasks/'):
            self.handle_task_detail()
        else:
            self.send_error(404, "Not Found")

    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 读取请求体
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(post_data.decode('utf-8')) if post_data else {}
        except:
            request_data = {}
        
        # 设置CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Content-Type', 'application/json')
        
        if path == '/animation/generate':
            self.handle_animation_generate(request_data)
        elif path == '/motion/generate':
            self.handle_motion_generate(request_data)
        elif path == '/motion/generate-advanced':
            self.handle_motion_generate_advanced(request_data)
        elif path == '/conversations':
            self.handle_conversation_create(request_data)
        elif path.startswith('/conversations/') and path.endswith('/messages'):
            self.handle_conversation_message(request_data)
        elif path == '/tasks':
            self.handle_task_create(request_data)
        else:
            self.send_error(404, "Not Found")

    def handle_health(self):
        """健康检查"""
        response = {
            "status": "healthy",
            "version": "1.0.0",
            "nlu_pipeline": "ready",
            "langgraph_pipeline": "ready", 
            "professional_animator": "ready",
            "features": {
                "animation_generation": True,
                "motion_capture": True,
                "fbx_export": True,
                "conversation_management": True,
                "task_monitoring": True
            }
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_animation_health(self):
        """动画系统健康检查"""
        response = {
            "status": "healthy",
            "animation_engine": "ready",
            "blender_integration": "available",
            "fbx_export": "functional"
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_animation_presets(self):
        """动画预设"""
        response = {
            "character_presets": {
                "default": {
                    "name": "Default Character",
                    "description": "Standard humanoid character",
                    "default_settings": {}
                },
                "warrior": {
                    "name": "Warrior",
                    "description": "Combat-ready character",
                    "default_settings": {}
                }
            },
            "quality_presets": {
                "game_ready": {
                    "name": "Game Ready",
                    "description": "Optimized for real-time rendering",
                    "settings": {}
                },
                "cinematic": {
                    "name": "Cinematic",
                    "description": "High quality for film",
                    "settings": {}
                }
            },
            "animation_styles": {
                "realistic": {
                    "name": "Realistic",
                    "description": "Natural human movement",
                    "parameters": {}
                },
                "stylized": {
                    "name": "Stylized",
                    "description": "Exaggerated movement",
                    "parameters": {}
                }
            }
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_animation_examples(self):
        """动画示例"""
        response = [
            {
                "text": "A character walking forward confidently",
                "description": "Basic locomotion animation",
                "character_id": "default",
                "expected_actions": ["walk", "stride", "confident_posture"]
            },
            {
                "text": "Jumping over a small obstacle",
                "description": "Acrobatic movement",
                "character_id": "default", 
                "expected_actions": ["run_up", "jump", "land"]
            },
            {
                "text": "Waving hello with both hands",
                "description": "Greeting gesture",
                "character_id": "default",
                "expected_actions": ["wave", "smile", "friendly_gesture"]
            }
        ]
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_animation_generate(self, request_data):
        """生成动画"""
        text = request_data.get('text', '')
        character_id = request_data.get('character_id', 'default')
        
        # 模拟处理时间
        time.sleep(1)
        
        response = {
            "success": True,
            "original_text": text,
            "animation_sequence": {
                "actions": ["walk", "turn", "wave"],
                "duration": 5.0,
                "character_id": character_id
            },
            "processed_actions": ["walk_forward", "turn_right", "wave_hand"],
            "quality_report": {
                "status": "excellent"
            },
            "file_path": f"/output/animation_{int(time.time())}.fbx",
            "download_url": f"/animation/download/animation_{int(time.time())}.fbx"
        }
        
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_motion_generate(self, request_data):
        """基础动作生成"""
        response = {
            "success": True,
            "message": "Motion generated successfully",
            "action_sequence": {
                "actions": ["basic_motion"],
                "duration": 3.0,
                "complexity": "simple"
            }
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_motion_generate_advanced(self, request_data):
        """高级动作生成"""
        response = {
            "success": True,
            "message": "Advanced motion generated successfully",
            "action_sequence": {
                "actions": ["complex_motion", "transition", "finale"],
                "duration": 8.0,
                "complexity": "advanced"
            }
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_conversations_list(self):
        """对话列表"""
        response = {
            "threads": [
                {
                    "id": "conv_1",
                    "title": "Animation Project 1",
                    "description": "Character walking animations",
                    "status": "active",
                    "character_id": "default",
                    "context": {},
                    "settings": {},
                    "message_count": 5,
                    "total_tokens": 150,
                    "created_at": "2024-01-01T10:00:00Z",
                    "updated_at": "2024-01-01T11:00:00Z",
                    "last_activity_at": "2024-01-01T11:00:00Z",
                    "is_pinned": False
                }
            ],
            "total": 1,
            "page": 1,
            "page_size": 10,
            "has_next": False,
            "has_prev": False
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_conversation_create(self, request_data):
        """创建对话"""
        response = {
            "id": f"conv_{int(time.time())}",
            "title": request_data.get('title', 'New Conversation'),
            "description": request_data.get('description', ''),
            "status": "active",
            "character_id": request_data.get('character_id', 'default'),
            "context": {},
            "settings": {},
            "message_count": 0,
            "total_tokens": 0,
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z",
            "last_activity_at": "2024-01-01T12:00:00Z",
            "is_pinned": False
        }
        self.send_response(201)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_conversation_history(self):
        """对话历史"""
        response = {
            "conversation_id": "conv_1",
            "messages": [
                {
                    "id": "msg_1",
                    "conversation_id": "conv_1",
                    "content": "Hello, I need a walking animation",
                    "message_type": "user",
                    "metadata": {},
                    "created_at": "2024-01-01T10:30:00Z",
                    "updated_at": "2024-01-01T10:30:00Z"
                },
                {
                    "id": "msg_2", 
                    "conversation_id": "conv_1",
                    "content": "I'll create a walking animation for you.",
                    "message_type": "assistant",
                    "metadata": {},
                    "created_at": "2024-01-01T10:31:00Z",
                    "updated_at": "2024-01-01T10:31:00Z"
                }
            ],
            "total_messages": 2,
            "page": 1,
            "page_size": 10,
            "has_next": False,
            "has_prev": False
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_conversation_message(self, request_data):
        """发送消息"""
        response = {
            "id": f"msg_{int(time.time())}",
            "conversation_id": "conv_1",
            "content": request_data.get('content', ''),
            "message_type": request_data.get('message_type', 'user'),
            "metadata": {},
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z"
        }
        self.send_response(201)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_tasks_list(self):
        """任务列表"""
        response = {
            "tasks": [
                {
                    "id": "task_1",
                    "task_id": "task_1",
                    "task_type": "animation_generation",
                    "task_name": "Generate Walking Animation",
                    "description": "Creating walking animation for character",
                    "priority": "normal",
                    "status": "completed",
                    "progress": 100,
                    "input_data": {"text": "walking animation"},
                    "output_data": {"file_path": "/output/walk.fbx"},
                    "metadata": {},
                    "retry_count": 0,
                    "max_retries": 3,
                    "created_at": "2024-01-01T10:00:00Z",
                    "started_at": "2024-01-01T10:01:00Z",
                    "completed_at": "2024-01-01T10:05:00Z",
                    "actual_duration": 240,
                    "is_cancelled": False,
                    "is_system_task": False
                },
                {
                    "id": "task_2",
                    "task_id": "task_2", 
                    "task_type": "animation_generation",
                    "task_name": "Generate Jump Animation",
                    "description": "Creating jump animation for character",
                    "priority": "high",
                    "status": "running",
                    "progress": 65,
                    "input_data": {"text": "jumping animation"},
                    "output_data": {},
                    "metadata": {},
                    "retry_count": 0,
                    "max_retries": 3,
                    "created_at": "2024-01-01T11:00:00Z",
                    "started_at": "2024-01-01T11:01:00Z",
                    "is_cancelled": False,
                    "is_system_task": False
                }
            ],
            "total": 2,
            "page": 1,
            "page_size": 10,
            "has_next": False,
            "has_prev": False
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_task_detail(self):
        """任务详情"""
        response = {
            "id": "task_1",
            "task_id": "task_1",
            "task_type": "animation_generation",
            "task_name": "Generate Walking Animation",
            "description": "Creating walking animation for character",
            "priority": "normal",
            "status": "completed",
            "progress": 100,
            "input_data": {"text": "walking animation", "character_id": "default"},
            "output_data": {"file_path": "/output/walk.fbx", "duration": 5.0},
            "metadata": {"quality_score": 0.95},
            "retry_count": 0,
            "max_retries": 3,
            "created_at": "2024-01-01T10:00:00Z",
            "started_at": "2024-01-01T10:01:00Z",
            "completed_at": "2024-01-01T10:05:00Z",
            "actual_duration": 240,
            "is_cancelled": False,
            "is_system_task": False
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_task_create(self, request_data):
        """创建任务"""
        response = {
            "id": f"task_{int(time.time())}",
            "task_id": f"task_{int(time.time())}",
            "task_type": request_data.get('task_type', 'animation_generation'),
            "task_name": request_data.get('task_name', 'New Task'),
            "description": request_data.get('description', ''),
            "priority": request_data.get('priority', 'normal'),
            "status": "pending",
            "progress": 0,
            "input_data": request_data.get('input_data', {}),
            "output_data": {},
            "metadata": {},
            "retry_count": 0,
            "max_retries": 3,
            "created_at": "2024-01-01T12:00:00Z",
            "is_cancelled": False,
            "is_system_task": False
        }
        self.send_response(201)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_task_stats(self):
        """任务统计"""
        response = {
            "total_tasks": 10,
            "pending_tasks": 2,
            "running_tasks": 1,
            "completed_tasks": 6,
            "failed_tasks": 1,
            "cancelled_tasks": 0,
            "average_duration": 180,
            "success_rate": 85.7
        }
        self.send_response(200)
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

def run_server(port=9000):
    """启动服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MockMotionAgentHandler)
    print(f"🎬 Mock Motion Agent Backend Server running on http://localhost:{port}")
    print(f"📖 Health Check: http://localhost:{port}/health")
    print(f"🔄 CORS enabled for all origins")
    print(f"⚡ Ready to serve frontend requests!")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
