version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:7-jammy
    container_name: motion-agent-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: motion_agent
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongodb.js:/docker-entrypoint-initdb.d/init-mongodb.js:ro
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - motion-agent-network

  # Redis 缓存和任务队列
  redis:
    image: redis:7-alpine
    container_name: motion-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - motion-agent-network

  # Redis Commander (Redis 管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: motion-agent-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - motion-agent-network

  # MongoDB Express (MongoDB 管理界面)
  mongo-express:
    image: mongo-express:latest
    container_name: motion-agent-mongo-express
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    ports:
      - "8080:8081"
    depends_on:
      - mongodb
    networks:
      - motion-agent-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  motion-agent-network:
    driver: bridge
