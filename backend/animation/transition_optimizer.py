"""
动画过渡优化器
Animation Transition Optimizer for Smooth Action Sequences
"""

import math
from dataclasses import dataclass
from enum import Enum
from typing import Any

import numpy as np
from loguru import logger


class TransitionType(Enum):
    """过渡类型"""
    LINEAR = "linear"
    EASE_IN = "ease_in"
    EASE_OUT = "ease_out"
    EASE_IN_OUT = "ease_in_out"
    BOUNCE = "bounce"
    ELASTIC = "elastic"
    CUSTOM = "custom"


class BlendMode(Enum):
    """混合模式"""
    REPLACE = "replace"
    ADD = "add"
    MULTIPLY = "multiply"
    OVERLAY = "overlay"
    CROSSFADE = "crossfade"


@dataclass
class TransitionConfig:
    """过渡配置"""
    duration: float
    transition_type: TransitionType
    blend_mode: BlendMode
    ease_strength: float = 1.0
    anticipation_frames: int = 0
    follow_through_frames: int = 0
    overlap_frames: int = 0


@dataclass
class ActionTransition:
    """动作过渡"""
    from_action: str
    to_action: str
    config: TransitionConfig
    compatibility_score: float
    transition_frames: list[dict[str, Any]]


class TransitionOptimizer:
    """动画过渡优化器"""

    def __init__(self):
        self.action_compatibility = self._load_action_compatibility()
        self.transition_rules = self._load_transition_rules()
        self.easing_functions = self._create_easing_functions()

    def _load_action_compatibility(self) -> dict[str, dict[str, float]]:
        """加载动作兼容性矩阵"""
        return {
            "idle": {
                "walk": 0.9, "run": 0.8, "jump": 0.7, "attack": 0.6,
                "dance": 0.8, "sit": 0.9, "wave": 0.9
            },
            "walk": {
                "idle": 0.9, "run": 0.9, "jump": 0.8, "attack": 0.5,
                "turn": 0.8, "stop": 0.9, "backward": 0.7
            },
            "run": {
                "walk": 0.9, "jump": 0.9, "slide": 0.8, "attack": 0.4,
                "stop": 0.7, "turn": 0.6, "sprint": 0.9
            },
            "jump": {
                "land": 0.9, "roll": 0.8, "idle": 0.7, "walk": 0.8,
                "run": 0.8, "attack": 0.6, "flip": 0.7
            },
            "attack": {
                "idle": 0.6, "defend": 0.8, "combo": 0.9, "dodge": 0.7,
                "walk": 0.5, "run": 0.4, "recovery": 0.8
            },
            "dance": {
                "idle": 0.8, "spin": 0.9, "jump": 0.7, "pose": 0.8,
                "walk": 0.6, "bow": 0.8
            },
            "sit": {
                "idle": 0.9, "stand": 0.9, "lean": 0.8, "relax": 0.9,
                "wave": 0.7, "read": 0.8
            }
        }

    def _load_transition_rules(self) -> dict[str, dict[str, Any]]:
        """加载过渡规则"""
        return {
            "locomotion_to_locomotion": {
                "duration_range": (0.3, 0.8),
                "preferred_type": TransitionType.EASE_IN_OUT,
                "blend_mode": BlendMode.CROSSFADE,
                "anticipation_needed": False
            },
            "idle_to_action": {
                "duration_range": (0.2, 0.5),
                "preferred_type": TransitionType.EASE_OUT,
                "blend_mode": BlendMode.REPLACE,
                "anticipation_needed": True
            },
            "action_to_idle": {
                "duration_range": (0.4, 0.8),
                "preferred_type": TransitionType.EASE_IN,
                "blend_mode": BlendMode.CROSSFADE,
                "follow_through_needed": True
            },
            "combat_to_combat": {
                "duration_range": (0.1, 0.3),
                "preferred_type": TransitionType.LINEAR,
                "blend_mode": BlendMode.REPLACE,
                "anticipation_needed": True
            },
            "acrobatic_sequence": {
                "duration_range": (0.2, 0.4),
                "preferred_type": TransitionType.EASE_IN_OUT,
                "blend_mode": BlendMode.ADD,
                "overlap_needed": True
            }
        }

    def _create_easing_functions(self) -> dict[TransitionType, callable]:
        """创建缓动函数"""
        return {
            TransitionType.LINEAR: lambda t: t,
            TransitionType.EASE_IN: lambda t: t * t,
            TransitionType.EASE_OUT: lambda t: 1 - (1 - t) * (1 - t),
            TransitionType.EASE_IN_OUT: lambda t: 3 * t * t - 2 * t * t * t,
            TransitionType.BOUNCE: self._bounce_ease,
            TransitionType.ELASTIC: self._elastic_ease
        }

    def optimize_action_sequence(self, actions: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """优化动作序列"""
        logger.info(f"Optimizing sequence of {len(actions)} actions...")

        if len(actions) < 2:
            return actions

        optimized_sequence = []

        for i in range(len(actions)):
            current_action = actions[i]
            optimized_sequence.append(current_action)

            # 添加过渡（除了最后一个动作）
            if i < len(actions) - 1:
                next_action = actions[i + 1]
                transition = self._create_transition(current_action, next_action)

                if transition:
                    optimized_sequence.extend(transition.transition_frames)

        logger.success(f"Optimized sequence now has {len(optimized_sequence)} frames")
        return optimized_sequence

    def _create_transition(self, from_action: dict[str, Any], to_action: dict[str, Any]) -> ActionTransition | None:
        """创建动作过渡"""
        from_type = from_action.get("type", "unknown")
        to_type = to_action.get("type", "unknown")

        # 获取兼容性评分
        compatibility = self._get_compatibility_score(from_type, to_type)

        if compatibility < 0.3:  # 兼容性太低，不创建过渡
            logger.warning(f"Low compatibility between {from_type} and {to_type}: {compatibility}")
            return None

        # 确定过渡配置
        config = self._determine_transition_config(from_action, to_action, compatibility)

        # 生成过渡帧
        transition_frames = self._generate_transition_frames(from_action, to_action, config)

        return ActionTransition(
            from_action=from_type,
            to_action=to_type,
            config=config,
            compatibility_score=compatibility,
            transition_frames=transition_frames
        )

    def _get_compatibility_score(self, from_type: str, to_type: str) -> float:
        """获取兼容性评分"""
        if from_type in self.action_compatibility:
            return self.action_compatibility[from_type].get(to_type, 0.3)
        return 0.3  # 默认兼容性

    def _determine_transition_config(self, from_action: dict[str, Any],
                                   to_action: dict[str, Any],
                                   compatibility: float) -> TransitionConfig:
        """确定过渡配置"""
        from_type = from_action.get("type", "unknown")
        to_type = to_action.get("type", "unknown")

        # 确定过渡类别
        transition_category = self._categorize_transition(from_type, to_type)
        rules = self.transition_rules.get(transition_category, self.transition_rules["idle_to_action"])

        # 基于兼容性调整持续时间
        duration_range = rules["duration_range"]
        duration = duration_range[0] + (1 - compatibility) * (duration_range[1] - duration_range[0])

        # 创建配置
        config = TransitionConfig(
            duration=duration,
            transition_type=rules["preferred_type"],
            blend_mode=rules["blend_mode"],
            ease_strength=compatibility,
            anticipation_frames=3 if rules.get("anticipation_needed", False) else 0,
            follow_through_frames=5 if rules.get("follow_through_needed", False) else 0,
            overlap_frames=2 if rules.get("overlap_needed", False) else 0
        )

        return config

    def _categorize_transition(self, from_type: str, to_type: str) -> str:
        """分类过渡类型"""
        locomotion_actions = ["walk", "run", "jog", "sprint", "backward"]
        combat_actions = ["attack", "defend", "dodge", "block", "combo"]
        acrobatic_actions = ["jump", "flip", "roll", "cartwheel", "handstand"]

        if from_type in locomotion_actions and to_type in locomotion_actions:
            return "locomotion_to_locomotion"
        elif from_type in combat_actions and to_type in combat_actions:
            return "combat_to_combat"
        elif from_type in acrobatic_actions and to_type in acrobatic_actions:
            return "acrobatic_sequence"
        elif from_type == "idle":
            return "idle_to_action"
        elif to_type == "idle":
            return "action_to_idle"
        else:
            return "idle_to_action"  # 默认

    def _generate_transition_frames(self, from_action: dict[str, Any],
                                  to_action: dict[str, Any],
                                  config: TransitionConfig) -> list[dict[str, Any]]:
        """生成过渡帧"""
        frames = []
        frame_rate = 30  # 假设30fps
        total_frames = int(config.duration * frame_rate)

        # 预备动作帧
        if config.anticipation_frames > 0:
            anticipation_frames = self._generate_anticipation_frames(
                from_action, to_action, config.anticipation_frames
            )
            frames.extend(anticipation_frames)

        # 主要过渡帧
        main_transition_frames = self._generate_main_transition_frames(
            from_action, to_action, total_frames, config
        )
        frames.extend(main_transition_frames)

        # 跟随动作帧
        if config.follow_through_frames > 0:
            follow_through_frames = self._generate_follow_through_frames(
                from_action, to_action, config.follow_through_frames
            )
            frames.extend(follow_through_frames)

        return frames

    def _generate_anticipation_frames(self, from_action: dict[str, Any],
                                    to_action: dict[str, Any],
                                    frame_count: int) -> list[dict[str, Any]]:
        """生成预备动作帧"""
        frames = []

        for i in range(frame_count):
            progress = i / frame_count

            frame = {
                "type": "transition_anticipation",
                "from_action": from_action.get("type"),
                "to_action": to_action.get("type"),
                "progress": progress,
                "frame_index": i,
                "duration": 0.033,  # 1/30秒
                "blend_weight": 0.1 + progress * 0.2,
                "anticipation_strength": 1.0 - progress
            }
            frames.append(frame)

        return frames

    def _generate_main_transition_frames(self, from_action: dict[str, Any],
                                       to_action: dict[str, Any],
                                       frame_count: int,
                                       config: TransitionConfig) -> list[dict[str, Any]]:
        """生成主要过渡帧"""
        frames = []
        easing_func = self.easing_functions[config.transition_type]

        for i in range(frame_count):
            t = i / max(1, frame_count - 1)  # 0到1的进度
            eased_t = easing_func(t)

            frame = {
                "type": "transition_main",
                "from_action": from_action.get("type"),
                "to_action": to_action.get("type"),
                "progress": t,
                "eased_progress": eased_t,
                "frame_index": i,
                "duration": config.duration / frame_count,
                "blend_weight": eased_t,
                "transition_type": config.transition_type.value,
                "blend_mode": config.blend_mode.value
            }

            # 添加具体的变换数据
            frame.update(self._interpolate_transform_data(from_action, to_action, eased_t))

            frames.append(frame)

        return frames

    def _generate_follow_through_frames(self, from_action: dict[str, Any],
                                      to_action: dict[str, Any],
                                      frame_count: int) -> list[dict[str, Any]]:
        """生成跟随动作帧"""
        frames = []

        for i in range(frame_count):
            progress = i / frame_count

            frame = {
                "type": "transition_follow_through",
                "from_action": from_action.get("type"),
                "to_action": to_action.get("type"),
                "progress": progress,
                "frame_index": i,
                "duration": 0.033,
                "blend_weight": 1.0 - progress * 0.3,
                "follow_through_strength": 1.0 - progress
            }
            frames.append(frame)

        return frames

    def _interpolate_transform_data(self, from_action: dict[str, Any],
                                  to_action: dict[str, Any],
                                  t: float) -> dict[str, Any]:
        """插值变换数据"""
        # 简化的变换插值
        from_pos = from_action.get("position", [0, 0, 0])
        to_pos = to_action.get("position", [0, 0, 0])

        from_rot = from_action.get("rotation", [0, 0, 0])
        to_rot = to_action.get("rotation", [0, 0, 0])

        # 线性插值位置
        interpolated_pos = [
            from_pos[i] + t * (to_pos[i] - from_pos[i]) for i in range(3)
        ]

        # 球面线性插值旋转（简化版）
        interpolated_rot = [
            from_rot[i] + t * (to_rot[i] - from_rot[i]) for i in range(3)
        ]

        return {
            "position": interpolated_pos,
            "rotation": interpolated_rot,
            "scale": [1.0, 1.0, 1.0]
        }

    def _bounce_ease(self, t: float) -> float:
        """弹跳缓动函数"""
        if t < 1/2.75:
            return 7.5625 * t * t
        elif t < 2/2.75:
            t -= 1.5/2.75
            return 7.5625 * t * t + 0.75
        elif t < 2.5/2.75:
            t -= 2.25/2.75
            return 7.5625 * t * t + 0.9375
        else:
            t -= 2.625/2.75
            return 7.5625 * t * t + 0.984375

    def _elastic_ease(self, t: float) -> float:
        """弹性缓动函数"""
        if t == 0 or t == 1:
            return t

        p = 0.3
        s = p / 4
        return -(2**(-10 * t)) * math.sin((t - s) * (2 * math.pi) / p) + 1

    def analyze_transition_quality(self, transitions: list[ActionTransition]) -> dict[str, Any]:
        """分析过渡质量"""
        if not transitions:
            return {"status": "no_transitions"}

        avg_compatibility = np.mean([t.compatibility_score for t in transitions])
        avg_duration = np.mean([t.config.duration for t in transitions])

        transition_types = [t.config.transition_type.value for t in transitions]
        most_common_type = max(set(transition_types), key=transition_types.count)

        quality_score = avg_compatibility * 0.6 + (1.0 if avg_duration < 1.0 else 0.5) * 0.4

        return {
            "total_transitions": len(transitions),
            "average_compatibility": avg_compatibility,
            "average_duration": avg_duration,
            "most_common_type": most_common_type,
            "quality_score": quality_score,
            "quality_level": self._determine_quality_level(quality_score)
        }

    def _determine_quality_level(self, score: float) -> str:
        """确定质量等级"""
        if score >= 0.9:
            return "excellent"
        elif score >= 0.8:
            return "good"
        elif score >= 0.6:
            return "acceptable"
        elif score >= 0.4:
            return "poor"
        else:
            return "unacceptable"

    def get_transition_recommendations(self, from_type: str, to_type: str) -> dict[str, Any]:
        """获取过渡建议"""
        compatibility = self._get_compatibility_score(from_type, to_type)
        category = self._categorize_transition(from_type, to_type)
        rules = self.transition_rules.get(category, {})

        return {
            "compatibility_score": compatibility,
            "recommended_duration": rules.get("duration_range", (0.3, 0.8)),
            "recommended_type": rules.get("preferred_type", TransitionType.EASE_IN_OUT).value,
            "recommended_blend": rules.get("blend_mode", BlendMode.CROSSFADE).value,
            "needs_anticipation": rules.get("anticipation_needed", False),
            "needs_follow_through": rules.get("follow_through_needed", False),
            "quality_prediction": self._determine_quality_level(compatibility)
        }

    def optimize_timing(self, actions: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """优化时间节奏"""
        optimized_actions = []

        for i, action in enumerate(actions):
            optimized_action = action.copy()

            # 基于动作类型调整时长
            action_type = action.get("type", "idle")
            ideal_duration = self._get_ideal_duration(action_type)

            # 考虑上下文调整
            if i > 0:
                prev_action = actions[i-1]
                ideal_duration = self._adjust_duration_for_context(
                    optimized_action, prev_action, ideal_duration
                )

            optimized_action["duration"] = ideal_duration
            optimized_actions.append(optimized_action)

        return optimized_actions

    def _get_ideal_duration(self, action_type: str) -> float:
        """获取理想动作时长"""
        ideal_durations = {
            "idle": 3.0,
            "walk": 1.0,
            "run": 0.8,
            "jump": 1.5,
            "attack": 0.6,
            "defend": 1.2,
            "dance": 2.0,
            "wave": 1.5,
            "sit": 2.0,
            "stand": 1.0
        }
        return ideal_durations.get(action_type, 1.0)

    def _adjust_duration_for_context(self, current_action: dict[str, Any],
                                   prev_action: dict[str, Any],
                                   base_duration: float) -> float:
        """根据上下文调整时长"""
        current_type = current_action.get("type", "")
        prev_type = prev_action.get("type", "")

        # 如果前一个动作是快速动作，当前动作可以稍微加快
        if prev_type in ["run", "attack", "jump"]:
            return base_duration * 0.9

        # 如果前一个动作是慢速动作，当前动作可以稍微放慢
        if prev_type in ["idle", "sit", "relax"]:
            return base_duration * 1.1

        return base_duration
