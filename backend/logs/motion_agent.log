2025-06-10 17:54:26 | INFO     | backend.config:validate_config:305 - Created directory: ./output/animations
2025-06-10 17:54:26 | INFO     | backend.config:validate_config:305 - Created directory: ./temp/animation_data
2025-06-10 17:54:26 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:54:26 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:54:26 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:54:26 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:54:26 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:54:26 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:54:26 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:54:26 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:54:45 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:54:45 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:54:45 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:54:45 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:54:45 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:54:45 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:54:45 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:54:45 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:54:45 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:54:45 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:54:45 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:54:45 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:54:45 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:54:45 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:54:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:54:58 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:54:58 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:54:58 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:54:58 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:54:58 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:54:58 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:54:59 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:54:59 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:54:59 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:54:59 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:54:59 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:54:59 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:54:59 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:54:59 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:11 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:11 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:11 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:11 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:11 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:11 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:12 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:12 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:12 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:12 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:12 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:12 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:12 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:12 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:21 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:21 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:21 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:21 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:21 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:21 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:22 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:22 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:22 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:22 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:22 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:22 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:22 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:22 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:55:44 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:44 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:44 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:44 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:44 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:44 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:45 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:45 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:45 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:45 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:45 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:45 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:45 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:45 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:55:56 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:56 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:56 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:56 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:56 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:56 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:56 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:56 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:56 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:56 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:56 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:56 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:56 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:56 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:07 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:56:07 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:56:07 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:56:07 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:56:07 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:56:07 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:56:07 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:56:07 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:56:07 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:56:07 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:56:07 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:56:07 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:56:07 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:56:07 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:18 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:56:18 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:56:18 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:56:18 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:56:18 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:56:18 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:56:18 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:56:18 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:56:18 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:56:18 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:56:18 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:56:18 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:56:18 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:56:18 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:56:48 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:56:48 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:56:48 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:56:48 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:56:48 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:56:48 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:56:49 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:56:49 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:56:49 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:56:49 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:56:49 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:56:49 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:56:49 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:56:49 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:57:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:58:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:58:55 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:58:55 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:58:55 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:58:55 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:58:55 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:58:55 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:58:56 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:58:56 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:58:56 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:58:56 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:58:56 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:58:56 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:58:56 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:58:56 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:59:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:00:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
