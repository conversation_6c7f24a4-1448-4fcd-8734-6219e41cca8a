#!/usr/bin/env python3
"""
Motion Agent Backend Startup Script
简单的后端启动脚本
"""

import subprocess
import sys
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 11):
        print("❌ 需要Python 3.11或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True


def setup_directories():
    """设置必要的目录"""
    print("📁 设置目录结构...")

    directories = [
        'backend/logs',
        'backend/output/animations',
        'backend/temp/animation_data'
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")


def check_dependencies():
    """检查核心依赖是否安装"""
    print("🔍 检查核心依赖...")

    core_packages = [
        'fastapi', 'uvicorn', 'pydantic', 'loguru'
    ]

    missing_packages = []

    for package in core_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")

    if missing_packages:
        print(f"\n🚫 缺少核心依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print("  uv sync")
        print("  # 或者")
        print("  pip install -e .")
        return False

    print("✅ 核心依赖检查通过")
    return True


def start_backend_server():
    """启动后端服务器"""
    print("\n🚀 启动Motion Agent后端服务器...")
    print("=" * 50)

    try:
        # 使用uvicorn启动服务器
        cmd = [
            sys.executable, '-m', 'uvicorn',
            'backend.app:app',
            '--host', '0.0.0.0',
            '--port', '9000',
            '--reload',
            '--log-level', 'info'
        ]

        print(f"执行命令: {' '.join(cmd)}")
        print("\n🌐 服务器将在以下地址启动:")
        print("   - API根路径: http://localhost:9000/")
        print("   - API文档: http://localhost:9000/docs")
        print("   - ReDoc: http://localhost:9000/redoc")
        print("   - 健康检查: http://localhost:9000/health")
        print("   - 专业动画师: http://localhost:9000/animation/")
        print("\n按 Ctrl+C 停止服务器")
        print("=" * 50)

        # 启动服务器
        subprocess.run(cmd)

    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n💥 启动服务器失败: {e}")
        print("\n💡 故障排除:")
        print("1. 确保所有依赖已安装: uv sync")
        print("2. 检查端口9000是否被占用")
        print("3. 检查backend/app.py文件是否存在")


def show_api_examples():
    """显示API使用示例"""
    print("\n📖 API使用示例:")
    print("=" * 30)

    examples = [
        {
            "title": "🎪 基础动作生成",
            "endpoint": "/generate-motion",
            "text": "慢慢走向前方，然后挥手打招呼"
        },
        {
            "title": "🚀 高级动作生成",
            "endpoint": "/generate-motion-advanced",
            "text": "生成一个后空翻720度之后，向前五步走，然后转身"
        },
        {
            "title": "⚔️ 专业动画师",
            "endpoint": "/animation/generate",
            "text": "快速冲刺攻击，然后防御姿态"
        }
    ]

    for example in examples:
        print(f"\n{example['title']}:")
        print(f"curl -X POST 'http://localhost:9000{example['endpoint']}' \\")
        print("     -H 'Content-Type: application/json' \\")
        print("     -d '{")
        print(f"       \"text\": \"{example['text']}\",")
        print("       \"character_id\": \"test_character\"")
        print("     }'")


def main():
    """主函数"""
    print("🎬 Motion Agent Backend Launcher")
    print("=" * 40)
    print("Motion Agent 后端启动器")
    print("=" * 40)

    # 检查Python版本
    if not check_python_version():
        sys.exit(1)

    # 设置目录
    setup_directories()

    # 检查依赖
    if not check_dependencies():
        print("\n💡 安装依赖命令:")
        print("   uv sync")
        print("   # 或者")
        print("   pip install -e .")
        sys.exit(1)

    # 显示API示例
    show_api_examples()

    # 启动服务器
    print("\n❓ 是否启动后端服务器? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是', '']:
            start_backend_server()
        else:
            print("\n💡 手动启动服务器:")
            print("   python -m uvicorn backend.app:app --reload")
            print("   # 或者")
            print("   uv run uvicorn backend.app:app --reload")
    except KeyboardInterrupt:
        print("\n👋 用户取消")
        sys.exit(0)


if __name__ == "__main__":
    main()
